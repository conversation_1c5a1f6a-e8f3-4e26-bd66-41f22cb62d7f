DONE=Done
BTN_FIND=Find
MB_EXIT_SAVE=Save the project before exit?
MB_SAVE_AND_SIM=Project not saved. Save the project and start simulation?
STA_READY=Ready
TITLE=V2Sim (Py) Project Editor
LB_COUNT=Count: {0}
MENU_PROJ=Project
MENU_OPEN=Open...
MENU_SAVEALL=Save all
MENU_EXIT=Exit
MB_INFO=Hint
MB_PDN_REQUIRED=PDN plugin requires cvxpy and ecos, but they are not all installed. Would you like to install them now?
MB_ERROR=Error
MB_CFG_MODIFY=Configuration generated by <PERSON>UMO automatically is differnet from the standard of V2Sim. Would you like to change the configuration file?\nNOTE: SUMO trips will be removed. Only V2Sim trips will be preserved.
BAR_PROJINFO=Project Information
BAR_NONE=None
BAR_FCS=FCS: 
BAR_SCS=SCS: 
BAR_GRID=Power Grid: 
BAR_RNET=Road Network: 
BAR_VEH=Vehicles: 
BAR_PLG=Plugins: 
BAR_SUMO=SUMO Config: 
BAR_TAZ=TAZ Descriptor: 
BAR_ADDON=Python Add-on: 
BAR_TAZTYPE=TAZ Type: 
BAR_OSM=OpenStreetMap: 
BAR_POLY=Polygon Descriptor: 
BAR_POI=POI Descriptor: 
BAR_CSCSV=CS CSV Descriptor: 
CSE_EDGE=Edge
CSE_SLOTS=Slots
CSE_BUS=Bus
CSE_X=x
CSE_Y=y
CSE_OFFLINE=Offline
CSE_MAXPC=Max Pc/kW
CSE_MAXPD=Max Pd/kW
CSE_PRICEBUY=Price Buy
CSE_PRICESELL=Price Sell
CSE_PCALLOC=Pc Allocator
CSE_PDALLOC=Pd Allocator
RNET_TITLE=Network
RNET_SAVE=Save canvas
RNET_DRAW=Locate
RNET_EDGES=Edges to be located:
SAVE_GRID=Save Grid
PU_VALS=Base Voltage: {0}kV, Base Power: {1}MVA
SIM_BASIC=Basic Information
SIM_BEGT=Start Time/s:
SIM_ENDT=End Time/s:
SIM_STEP=Time step/s:
SIM_SEED=Randomize seed:
SIM_ROUTE_ALGO=Routing algorithm:
SIM_PLUGIN=Plugins
SIM_STAT=Statistics
SIM_FCS=FCS
SIM_SCS=SCS
SIM_VEH=Vehicle
SIM_GEN=Generator
SIM_BUS=Bus
SIM_LINE=Line
SIM_PVW=PV & Wind
SIM_ESS=ESS
SIM_START=Start Simulation!
SIM_PLGNAME=Plugin Name
SIM_EXEINTV=Interval/s
SIM_ENABLED=Enabled
SIM_PLGOL=Online Time Range
SIM_PLGPROP=Extra Properties
SIM_YES=Yes
SIM_NO=No
SIM_LOAD_LAST_STATE=Load last state (Experimental)
SIM_SAVE_ON_ABORT=Save state when aborted
SIM_SAVE_ON_FINISH=Save state when simulation ended

SIM_STATIC_ROUTE=Force to use routing cache
SIM_COPY_STATE=Copy state to the case folder when aborted or ended
TAB_SIM=Simulation
TAB_CSCSV=CS Downloader
TAB_FCS=Fast CS
TAB_SCS=Slow CS
TAB_RNET=Network
TAB_VEH=Vehicles
CSCSV_ID=ID
CSCSV_X=X
CSCSV_Y=Y
CSCSV_ADDR=Address
CSCSV_DOWNLOAD=Download
CSCSV_KEY=AMap Key:
CSCSV_CONFIRM_TITLE=Download CS
CSCSV_CONFIRM=Are you sure to download CS from AMap?
GRID_BASIC=Basic Information
GRID_SB=Base S:
GRID_VB=Base U:
VEH_BASIC=Basic Information
VEH_COUNT=Vehicle count:
VEH_DAY_COUNT=Day count:
VEH_V2GPROP=V2G prop:
VEH_V2GPROP_INFO=Proportion of vehicles willing to join V2G (0.00~1.00)
VEH_SEED=Seed:
VEH_ODSRC=OD pair source
VEH_ODAUTO=Auto detection
VEH_ODTAZ=By TAZs' types
VEH_ODPOLY=By buildings' contours and types
VEH_GEN=Generate
VEH_OMEGA_DESC=Omega indicates the user's sensitivity to the cost of charging. Bigger Omega means less sensitive.
VEH_KREL_DESC=KRel indicates the user's estimation of the distance. Bigger KRel means the user underestimates the distance.
VEH_KSC_DESC=KSC indicates the SoC threshold for slow charging.
VEH_KFC_DESC=KFC indicates the SoC threshold for fast charging halfway.
VEH_KV2G_DESC=KV2G indicates the SoC threshold of the battery that can be used for V2G.
VEH_ROUTE_CACHE=Routing Cache
VEH_ROUTE_NO_CACHE=Never cache: Find the optimal path considering the road condition real-time. 
VEH_ROUTE_RUNTIME_CACHE=Runtime cache: Cache after first routing, balancing speed and optimality.
VEH_ROUTE_STATIC_CACHE=Static cache: Calculate the paths during EV generation. Boost simulation.
CS_GEN=Generate a new group of CSs
CS_MODE=Generation Mode
CS_USEALL=All available
CS_SELECTED=Given
CS_RANDOM=Random N
CS_SRC=CS Source Edges
CS_USEEDGES=All edges
CS_USECSV=Edges determined by downloaded CS positions
CS_USEPOLY=Edges determined by buildings' contours
CS_SLOTS=Number of slots in each CS
CS_SEED=Random seed
CS_PRICEBUY=Users' buying price
CS_PRICESELL=Users' selling price
CS_PB5SEGS=5 segements random
CS_PBFIXED=Fixed
CS_BUSMODE=Bus Selection Mode
CS_BUSBYPOS=By bus location (if all bus locations are specified)
CS_BUSUSEALL=All available
CS_BUSSELECTED=Given
CS_BUSRANDOM=Random N
CS_BTN_GEN=Generate
NOT_OPEN=Not open
SAVED=Saved
UNSAVED=Unsaved
STA_GEN_VEH=Generating vehicles...
MSG_NO_TRAFFIC_GEN=No traffic generator loaded
MSG_INVALID_VEH_CNT=Invalid vehicle count. Must be an integer.
MSG_INVALID_VEH_SEED=Invalid vehicle generation seed. Must be an integer.
MSG_INVALID_VEH_DAY_CNT=Invalid day count. Must be an integer.
EXT_EPS=Embedded Postscript
RNET_SAVE_ERR=Fail to save canvas. Invalid file name.
PROJ_NO_OPEN=No project folder opened
SAVE_PLG=Saving plugins...
NO_SUMO_CFG=No SUMO config file detected
NO_STA=No statistics selected