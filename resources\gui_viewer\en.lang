TITLE=Result Viewer
STA_READY=Ready
ITEM_ALL=<All>
ITEM_SUM=<Sum>
MENU_FILE=File
MENU_OPEN=Open...
MENU_EXIT=Exit
TAB_CURVE=Graphs
TAB_GRID=Grid
TAB_TRIP=Trips
TAB_STATE=State
BTN_PLOT=Plot
BTN_ACCUM=Plot accum. graph
BTN_TOTAL=Plot total
FCS_TITLE=Fast charging station
FCS_NVEH=# Vehicle
FCS_PC=Charging power
FCS_PRICE=Charging Price
SCS_TITLE=Slow charging station
SCS_NVEH=# Vehicle
SCS_PC=Charging power
SCS_PD=Discharging power
SCS_PPURE=Net power
SCS_PV2G=V2G capacity
SCS_PBUY=Charging price
SCS_PSELL=Discharging price
EV_TITLE=Electric vehicle
EV_STA=Status
EV_COST=Charging cost
EV_EARN=V2G earn
EV_NETCOST=Net cost
GEN_TITLE=Generator
GEN_COST=Average cost
BUS_TITLE=Bus
BUS_PD=Active load
BUS_QD=Reactive load
BUS_V=Voltage
BUS_PG=Active output
BUS_QG=Reactive output
LINE_TITLE=Line
LINE_CURRENT=Current
PVW_TITLE=PV & Wind turbines
PVW_CR=Curtailment rate
ESS_TITLE=Energy storage system
ACTIVE_POWER=Active power
REACTIVE_POWER=Reactive power
SOC=SoC
START_TIME=Start time:
END_TIME=End time:
PLOT_MAX=Mark maximum
PLOT_TITLE=Plot title
TIME=Time
TIME_POINT=Time point:
GRID_COLLECT=Collect
TAB_QUERIES=Queries
NO_IMAGE=No image
ERROR=Error
LOAD_FAILED=Cannot load image: {}
NO_SAVED_STATE=No saved state! Please save a state in the simulation to use this function.
EMPTY_QUERY=Queries cannot be empty!
LOADING=Loading...
NO_CPROC=cproc.clog not found!
TITLE_SEL_FOLDER=Please select the result folder
INTERNAL_ERR=Internal Error
NOTHING_PLOT=Please tick at least one item to plot!
SAVED_STATE_LOAD_FAILED=Failed to load saved state!
FILE_EXT=File extension:
IMAGE_DPI=DPI:
INVALID_DPI=Invalid DPI. Must be an integer.