DONE=完成
BTN_FIND=查找
MB_EXIT_SAVE=是否要在退出前保存项目？
MB_SAVE_AND_SIM=项目未保存。是否保存并开始仿真？
STA_READY=就绪
TITLE=V2Sim(Python版)项目编辑器
LB_COUNT=数量：{0}
MENU_PROJ=项目
MENU_OPEN=打开...
MENU_SAVEALL=全部保存
MENU_EXIT=退出
MB_INFO=提示
MB_PDN_REQUIRED=配电网插件需要cvxpy和ecos，但是并未全部安装。是否现在安装？
MB_ERROR=错误
MB_CFG_MODIFY=SUMO自动生成配置文件与V2Sim平台标准有差异。是否修改SUMO配置文件？\n请注意，这将删除SUMO生成的行程，仅保留V2Sim生成的行程文件。
BAR_PROJINFO=项目信息
BAR_NONE=无
BAR_FCS=快充站：
BAR_SCS=慢充站：
BAR_GRID=电网：
BAR_RNET=路网：
BAR_VEH=车辆：
BAR_PLG=插件：
BAR_SUMO=SUMO配置：
BAR_TAZ=交通区域描述：
BAR_ADDON=Python附加：
BAR_TAZTYPE=交通区域类型：
BAR_OSM=OpenStreetMap： 
BAR_POLY=建筑轮廓描述：
BAR_POI=POI描述：
BAR_CSCSV=充电站CSV描述：
CSE_EDGE=所在道路
CSE_SLOTS=充电桩数量
CSE_BUS=所在母线
CSE_X=x坐标
CSE_Y=y坐标
CSE_OFFLINE=关闭时间
CSE_MAXPC=最大充电功率/kW
CSE_MAXPD=最大放电功率/kW
CSE_PRICEBUY=用户购电价格
CSE_PRICESELL=用户售电价格
CSE_PCALLOC=充电功率分配方案
CSE_PDALLOC=放电功率分配方案
RNET_TITLE=网络
RNET_SAVE=保存画布
RNET_DRAW=定位
RNET_EDGES=要定位的道路：
SAVE_GRID=保存电网
PU_VALS=基准电压：{0}kV，基准功率：{1}MVA
SIM_BASIC=基本信息
SIM_BEGT=开始时间/秒：
SIM_ENDT=结束时间/秒：
SIM_STEP=仿真步长/秒：
SIM_SEED=随机化种子：
SIM_ROUTE_ALGO=寻路算法：
SIM_PLUGIN=插件
SIM_STAT=统计
SIM_FCS=快充站
SIM_SCS=慢充站
SIM_VEH=车辆
SIM_GEN=发电机
SIM_BUS=母线
SIM_LINE=线路
SIM_PVW=光伏和风电
SIM_ESS=储能
SIM_START=开始仿真！
SIM_PLGNAME=插件名称
SIM_EXEINTV=执行间隔/秒
SIM_ENABLED=是否启用
SIM_PLGOL=启用时间
SIM_PLGPROP=其他属性
SIM_YES=是
SIM_NO=否
SIM_LOAD_LAST_STATE=加载上次的状态（实验性功能）
SIM_SAVE_ON_ABORT=中断时保存状态
SIM_SAVE_ON_FINISH=仿真结束时保存状态

SIM_STATIC_ROUTE=强制使用寻路缓存
SIM_COPY_STATE=仿真结束或中断时复制状态至算例文件夹
TAB_SIM=仿真
TAB_CSCSV=充电站下载
TAB_FCS=快充站
TAB_SCS=慢充站
TAB_RNET=网络
TAB_VEH=车辆
TAB_GRID=电网
CSCSV_ID=编号
CSCSV_X=X坐标
CSCSV_Y=Y坐标
CSCSV_ADDR=地址
CSCSV_DOWNLOAD=下载
CSCSV_KEY=高德地图Key：
CSCSV_CONFIRM_TITLE=下载充电站
CSCSV_CONFIRM=是否要从高德地图检索充电站信息？
GRID_BASIC=基本信息
GRID_SB=基准功率:
GRID_VB=基准电压:
VEH_BASIC=基本信息
VEH_COUNT=车辆数量:
VEH_DAY_COUNT=天数:
VEH_V2GPROP=V2G比例:
VEH_V2GPROP_INFO=愿意参加V2G的电动车比例(0.00~1.00)
VEH_SEED=随机种子:
VEH_ODSRC=OD对来源
VEH_ODAUTO=自动检测
VEH_ODTAZ=根据交通区域类型生成
VEH_ODPOLY=根据建筑轮廓与类型生成
VEH_GEN=生成车辆
VEH_OMEGA_DESC=Omega表示用户对充电成本的敏感度。更大的Omega意味着更小的价格影响。
VEH_KREL_DESC=KRel表示用户对距离的估计。大于1的KRel意味着用户低估了距离。
VEH_KSC_DESC=KSC表示慢充的SoC阈值。
VEH_KFC_DESC=KFC表示半路快充的SoC阈值。
VEH_KV2G_DESC=KV2G表示可用于V2G的电池的SoC阈值。
VEH_ROUTE_CACHE=寻路缓存
VEH_ROUTE_NO_CACHE=禁用缓存: 采用实时最优路径
VEH_ROUTE_RUNTIME_CACHE=运行时缓存: 在首次寻路后缓存路径，平衡运行速度和最优性
VEH_ROUTE_STATIC_CACHE=静态缓存: 在EV生成时就计算路径，提高仿真时速度
CS_GEN=生成一组新的充电站
CS_MODE=生成模式
CS_USEALL=所有可用项
CS_SELECTED=指定项目
CS_RANDOM=从所有可用项中抽取N个
CS_SRC=充电站所在道路
CS_USEEDGES=将每条道路都视为可用充电站
CS_USECSV=根据下载的充电站位置判断所在道路
CS_USEPOLY=根据建筑轮廓确定所在道路
CS_SLOTS=充电桩数量
CS_SEED=随机种子
CS_PRICEBUY=用户购电价格
CS_PRICESELL=用户售电价格
CS_PB5SEGS=5段式随机价格
CS_PBFIXED=固定价格
CS_BUSMODE=母线选择方式
CS_BUSBYPOS=根据母线位置选择(若所有母线地理位置已指定)
CS_BUSUSEALL=在所有可用母线中选择
CS_BUSSELECTED=在给定母线中选择
CS_BUSRANDOM=在N条随机母线中选择
CS_BTN_GEN=生成充电站
NOT_OPEN=未打开
SAVED=已保存
UNSAVED=未保存
STA_GEN_VEH=生成车辆中……
MSG_NO_TRAFFIC_GEN=未加载交通生成器
MSG_INVALID_VEH_CNT=无效车辆数，必须是整数。
MSG_INVALID_VEH_SEED=无效车辆生成种子，必须是整数。
MSG_INVALID_VEH_DAY_CNT=无效行程生成天数，必须是整数。
EXT_EPS=嵌入式Postscript(EPS)
RNET_SAVE_ERR=无法保存画布，因为文件名无效。
PROJ_NO_OPEN=未打开工程文件夹
SAVE_PLG=保存插件……
NO_SUMO_CFG=未找到SUMO配置
NO_STA=未选中统计项目